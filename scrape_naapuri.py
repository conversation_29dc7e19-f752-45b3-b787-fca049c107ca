#  <PERSON>ript gets links from modified_data\unique_links.json
#  and scrapes the data from each link into result\naapuriseura.json

import json
import requests
import os
from bs4 import BeautifulSoup

SOURCE = 'modified_data\\unique_links.json'
RESULT = 'result\\naapuriseura.json'

#  Get links from modified_data\unique_links.json
with open(SOURCE, 'r', encoding='utf-8') as f:
    links = json.load(f)

#  Create result folder if it doesn't exist
if not os.path.exists('result'):
    os.makedirs('result')

#  Create naapuriseura.json if it doesn't exist
if not os.path.exists('result\\naapuriseura.json'):
    with open('result\\naapuriseura.json', 'w', encoding='utf-8') as f:
        json.dump([], f)

#  Load naapuriseura.json
with open('result\\naapuriseura.json', 'r', encoding='utf-8') as f:
    naapuriseura = json.load(f)

#  Scrape data from each link
for item in links:
    #  print(item['href'])
    try:
        # Get random user agent from https://user-agents.net/random
        headers = {
            "User-Agent": "curl/7.68.0"
        }
        response = requests.get(item['href'], headers=headers, timeout=30)
        # print(response.status_code)
        # break

        if response.status_code != 200:
            print(f"Failed to get response from {item['href']}")
            continue
        
        soup = BeautifulSoup(response.text, 'html.parser')
        # <h1 class="entry-title">Captured elites and Europe's self-destruction</h1>
        title = soup.find('h1', class_='entry-title').text.strip()

        # <span class="item-metadata posts-date">14.7.2025</span>
        date = soup.find('span', class_='item-metadata posts-date').text.strip()

        # <div class="entry-content">
        content = soup.find('div', class_='entry-content').text.strip()

        #  Add data to naapuriseura.json
        naapuriseura.append({
            'href': item['href'],
            'title': title,
            'date': date,
            'content': content
        })

        #  Save naapuriseura.json
        with open(RESULT, 'w', encoding='utf-8') as f:
            json.dump(naapuriseura, f, ensure_ascii=False, indent=4)
        
        print(f"Scraped {item['href']}")

    except Exception as e:
        print(f"Failed to scrape {item['href']}: {e}")

print('Done!')
