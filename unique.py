# Get links from raw_data\naapuriseura-2025-07-15.json --> items --> 'entry-title href'
# and check if they are unique
# If not, remove duplicates
# Save unique links to unique_links.json

import json
import os

# Get links from raw_data\naapuriseura-2025-07-15.json --> items --> 'entry-title href'
with open(os.path.join("raw_data", "naapuriseura-2025-07-15.json"), "r", encoding="utf-8") as f:
    data = json.load(f)
    # print(data[0]['entry-title href'])

links = []
for item in data:
    link = item.get('entry-title href')
    if link and link not in links:
        links.append(link)

# print(len(links))
# print(links[0:10])

# Save unique links to unique_links.json as list of objects [{href: 'link'}...]
with open(os.path.join("modified_data", "unique_links.json"), "w", encoding="utf-8") as f:
    links = [{"href": link} for link in links]
    json.dump(links, f, ensure_ascii=False, indent=4)