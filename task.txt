Get all articles from https://naapuriseura.fi/en/
1. Get all links for articles using Easy Scraper in browser. Result is in raw_data\naapuriseura-2025-07-15.json.
2. Compare links from json file for uniques with unique.py file. Result in modified_data\unique_links.json.
3. Scrape all uniques links from modified_data\unique_links.json using scrape_naapuri.py and save result in result\naapuriseura.json.
4. Using prompt below and result\naapuriseura.json, ask Augment Code for analyzing data.

Promtpt:
Based on the qualitative and qualitative analysis of articles from this media outlet (result\naapuriseura.json) answer the following questions:
1) How do the articles portray Russia? <PERSON> or <PERSON>'s government/ regime? 
2) How do the articles portray Finland? Finnish government of politicians?
3) What do the articles have to say about the Finnish pro-NATO, pro-western policy in relation to Russia's invasion of Ukraine? 
4) What are the characteristics of the Finnish-Russian relations in general or in different regions? 
5) How often the narrative discusses Russia or Putin?
6)  How do the articles portray Russian representatives in Finland? Rossotrudnichestvo, the Russian House in Helsinki or the Russian Embassy?
7) What rhetorical and symbolic framings are used to portray Russia and Finland in a specific way?